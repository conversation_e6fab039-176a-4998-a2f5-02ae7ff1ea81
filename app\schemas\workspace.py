from pydantic import BaseModel
from typing import Optional

class ReportStatusUpdateRequest(BaseModel):
    """
    Request model for updating report status flags.

    Used to update the testing and deployment status of reports.
    All fields are optional - only provided fields will be updated.

    Attributes:
        unit_tested: Whether the report has passed unit testing
        uat_tested: Whether the report has passed user acceptance testing
        deployed: Whether the report has been deployed to production
    """
    unit_tested: Optional[bool] = None
    uat_tested: Optional[bool] = None
    deployed: Optional[bool] = None

class SuccessResponse(BaseModel):
    """
    Standard success response model for workspace operations.

    Attributes:
        success: <PERSON><PERSON><PERSON> indicating if the operation was successful
        message: Human-readable message describing the result
    """
    success: bool
    message: str