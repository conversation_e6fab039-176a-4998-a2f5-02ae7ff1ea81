from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depen<PERSON>, <PERSON>, HTTPException
from fastapi.responses import JSONResponse

from app.models.users import User
from app.core.dependencies import get_current_user
from app.core.logger_setup import logger
from app.services.workspace import WorkspaceProcessor
from app.schemas.workspace import ReportStatusUpdateRequest, SuccessResponse


workspace_router = APIRouter()

@workspace_router.get("/workspace/reports", response_model=dict)
async def get_workspace_reports(current_user: User = Depends(get_current_user)):
    """
    Retrieve workspace reports based on user role and permissions.

    Role-based access control:
    - Admin: sees all reports in their organization
    - Manager: sees reports assigned to them or their subordinates
    - Developer: sees only reports assigned to them

    Args:
        current_user: The authenticated user making the request

    Returns:
        JSONResponse: List of reports with project details and status flags

    Raises:
        HTTPException: If user authentication fails or internal error occurs
    """
    try:
        logger.info(f"[WORKSPACE_API] Endpoint hit: /workspace/reports by user {current_user.id}")

        response = WorkspaceProcessor.process_get_reports_by_user_role(current_user)
        logger.info(f"[WORKSPACE_API] Response from processor: success={response.success}, status={response.status_code}")

        return JSONResponse(content={"data": response.data, "error": response.error}, status_code=response.status_code)
    except Exception as e:
        logger.error(f"[WORKSPACE_API] Exception in workspace endpoint: {e}", exc_info=True)
        return JSONResponse(content={"data": None, "error": "Internal server error"}, status_code=500)
    

@workspace_router.patch(
    "/workspace/reports/{report_id}/status",
    response_model=SuccessResponse,
    summary="Update report status flags (unit_tested, uat_tested, deployed)"
)
def update_report_status(
    status_update: ReportStatusUpdateRequest,  # This will be your request body
    report_id: str = Path(..., description="ID of the report to update"),
    user: User = Depends(get_current_user)
):
    """
    Update the status flags (unit_tested, uat_tested, deployed) for a specific report.

    This endpoint allows updating the testing and deployment status flags for reports.
    Users can only update reports they have access to based on their role permissions.

    Args:
        status_update: Request body containing the status flags to update
        report_id: UUID of the report to update (from URL path)
        user: The authenticated user making the request

    Returns:
        SuccessResponse: Contains success status, message, and updated field details

    Raises:
        HTTPException: If report not found, access denied, or validation fails
    """
    logger.info(f"[WORKSPACE_API] Update report status endpoint hit for report {report_id} by user {user.id}")

    result = WorkspaceProcessor.process_update_report_status(report_id, status_update, user)

    if not result.success:
        logger.warning(f"[WORKSPACE_API] Report status update failed: {result.error}")
        raise HTTPException(status_code=result.status_code, detail=result.error)

    logger.info(f"[WORKSPACE_API] Report status update successful for report {report_id}")
    return SuccessResponse(
    success=True,
    message=(result.data.get("message") or "Update successful") if isinstance(result.data, dict) else "Update successful"
)