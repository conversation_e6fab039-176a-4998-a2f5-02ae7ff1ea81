import uuid
from sqlalchemy import Colum<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON><PERSON>, text, or_
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Query, joinedload, aliased
from app.models.users import User
from app.core.session import Base
from app.models.base import AuditMixin
from app.models.project_details import ProjectDetail

class ReportDetail(Base, AuditMixin):
    __tablename__ = "report_details"
    __table_args__ = {"schema": "biport_dev"}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    report_id = Column(UUID(as_uuid=True), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.project_details.id"), nullable=False)

    is_analyzed = Column(Boolean, server_default=text("false"))
    analyzed_status = Column(String, nullable=True)  # Store JSON string
    is_converted = Column(Bo<PERSON>an, server_default=text("false"))
    converted_status = Column(String, nullable=True)  # Store JSON string
    is_migrated = Column(Boolean, server_default=text("false"))
    migrated_status = Column(String, nullable=True)  # Store JSON string
    unit_tested = Column(Boolean, server_default=text("false"))
    uat_tested = Column(Boolean, server_default=text("false"))
    deployed = Column(Boolean, server_default=text("false"))
    is_scoped = Column(Boolean, server_default=text("false"))
    semantic_type = Column(String, nullable=True)
    has_semantic_model = Column(Boolean, server_default=text("false"))
    view_count = Column(Integer, server_default=text("0"))

    project = relationship("ProjectDetail", back_populates="reports")

    @classmethod
    def get_reports_by_user_role(cls, session, user, role_name: str) -> Query:
        query = session.query(cls).join(ProjectDetail, ReportDetail.project_id == ProjectDetail.id).options(joinedload(cls.project))

        Creator = aliased(User)

        if role_name == "Admin":
            pass
        elif role_name == "Manager":
            subordinate_ids = session.query(User.id).filter(User.manager_id == user.id).all()
            subordinate_ids = [sid[0] for sid in subordinate_ids]
            query = query.filter(
                or_(
                    ProjectDetail.assigned_to == user.id,
                    ProjectDetail.assigned_to.in_(subordinate_ids)
                )
            )
        elif role_name == "Developer":
            query = query.filter(ProjectDetail.assigned_to == user.id)

        query = query.join(Creator, ProjectDetail.creator).filter(
            Creator.organization_id == user.organization_id
        )

        return query.distinct()